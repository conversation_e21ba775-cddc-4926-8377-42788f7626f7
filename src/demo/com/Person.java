package demo.com;

/**
 * 人的基础类，使用泛型支持不同类型的姓名
 * @param <T> 姓名的类型，可以是String、StringBuilder等
 */
public class Person<T> {

    private T name;
    private int age;
    private String gender;

    // 默认构造函数
    public Person() {
    }

    // 带参数的构造函数
    public Person(T name, int age) {
        this.name = name;
        this.age = age;
    }

    // 完整构造函数
    public Person(T name, int age, String gender) {
        this.name = name;
        this.age = age;
        this.gender = gender;
    }

    // Getter和Setter方法
    public T getName() {
        return name;
    }

    public void setName(T name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    // 通用方法
    public void introduce() {
        System.out.println("我是 " + name + "，今年 " + age + " 岁");
        if (gender != null) {
            System.out.println("性别：" + gender);
        }
    }

    @Override
    public String toString() {
        return "Person{name=" + name + ", age=" + age + ", gender=" + gender + "}";
    }
}
