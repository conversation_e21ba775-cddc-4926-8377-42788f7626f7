package demo.com;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;

/**
 * 泛型老师管理器类，展示更高级的泛型使用
 * @param <T> 姓名类型
 * @param <S> 学科类型
 */
public class TeacherManager<T, S> {
    
    private List<Teacher<T, S>> teachers;
    
    public TeacherManager() {
        this.teachers = new ArrayList<>();
    }
    
    // 添加老师
    public void addTeacher(Teacher<T, S> teacher) {
        teachers.add(teacher);
        System.out.println("已添加老师：" + teacher.getName());
    }
    
    // 根据姓名查找老师
    public Optional<Teacher<T, S>> findTeacherByName(T name) {
        return teachers.stream()
                .filter(teacher -> teacher.getName().equals(name))
                .findFirst();
    }
    
    // 根据学科查找老师
    public List<Teacher<T, S>> findTeachersBySubject(S subject) {
        List<Teacher<T, S>> result = new ArrayList<>();
        for (Teacher<T, S> teacher : teachers) {
            if (teacher.getSubjects().contains(subject)) {
                result.add(teacher);
            }
        }
        return result;
    }
    
    // 获取所有老师
    public List<Teacher<T, S>> getAllTeachers() {
        return new ArrayList<>(teachers);
    }
    
    // 显示所有老师信息
    public void displayAllTeachers() {
        System.out.println("=== 所有老师信息 ===");
        if (teachers.isEmpty()) {
            System.out.println("暂无老师信息");
            return;
        }
        
        for (int i = 0; i < teachers.size(); i++) {
            System.out.println((i + 1) + ". " + teachers.get(i).toString());
        }
    }
    
    // 统计信息
    public void showStatistics() {
        System.out.println("=== 统计信息 ===");
        System.out.println("老师总数：" + teachers.size());
        
        if (!teachers.isEmpty()) {
            // 计算平均年龄
            double avgAge = teachers.stream()
                    .mapToInt(Teacher::getAge)
                    .average()
                    .orElse(0.0);
            System.out.println("平均年龄：" + String.format("%.1f", avgAge) + " 岁");
            
            // 计算平均教学经验
            double avgExperience = teachers.stream()
                    .mapToInt(Teacher::getYearsOfExperience)
                    .average()
                    .orElse(0.0);
            System.out.println("平均教学经验：" + String.format("%.1f", avgExperience) + " 年");
        }
    }
    
    // 移除老师
    public boolean removeTeacher(T name) {
        Optional<Teacher<T, S>> teacher = findTeacherByName(name);
        if (teacher.isPresent()) {
            teachers.remove(teacher.get());
            System.out.println("已移除老师：" + name);
            return true;
        } else {
            System.out.println("未找到老师：" + name);
            return false;
        }
    }
    
    // 获取老师数量
    public int getTeacherCount() {
        return teachers.size();
    }
}
