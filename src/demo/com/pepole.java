package demo.com;

// 创建pepole类支持泛型
public class pepole<T> {

    private T name;
    private int age;

    public pepole() {

    }

    public pepole(T name, int age) {
        this.name = name;
        this.age = age;
    }

    public T getName() {
        return name;
    }
    public void setName(T name) {
        this.name = name;
    }

    public int getAge() {
        return age;
    }

    public void setAge(int age) {
        this.age = age;
    }
}
