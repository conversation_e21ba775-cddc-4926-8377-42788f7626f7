package demo.com;

import java.util.List;
import java.util.Optional;

/**
 * 完整的泛型演示程序，展示Person、Teacher和TeacherManager的使用
 */
public class GenericDemo {
    
    public static void main(String[] args) {
        System.out.println("=== 完整泛型类演示程序 ===\n");
        
        // 创建老师管理器
        TeacherManager<String, String> manager = new TeacherManager<>();
        
        // 创建多个老师对象
        Teacher<String, String> teacher1 = new Teacher<>(
            "张教授", 40, "男", "数学", "T001", 15
        );
        teacher1.addSubject("统计学");
        teacher1.addSubject("概率论");
        
        Teacher<String, String> teacher2 = new Teacher<>(
            "李老师", 35, "女", "英语", "T002", 10
        );
        teacher2.addSubject("文学");
        
        Teacher<String, String> teacher3 = new Teacher<>(
            "王博士", 45, "男", "计算机科学", "T003", 20
        );
        teacher3.addSubject("人工智能");
        teacher3.addSubject("数据结构");
        teacher3.addSubject("算法");
        
        // 添加老师到管理器
        System.out.println("1. 添加老师到管理系统：");
        manager.addTeacher(teacher1);
        manager.addTeacher(teacher2);
        manager.addTeacher(teacher3);
        System.out.println();
        
        // 显示所有老师
        System.out.println("2. 显示所有老师信息：");
        manager.displayAllTeachers();
        System.out.println();
        
        // 查找特定老师
        System.out.println("3. 查找特定老师：");
        Optional<Teacher<String, String>> foundTeacher = manager.findTeacherByName("李老师");
        if (foundTeacher.isPresent()) {
            System.out.println("找到老师：");
            foundTeacher.get().introduce();
        }
        System.out.println();
        
        // 根据学科查找老师
        System.out.println("4. 查找教授'人工智能'的老师：");
        List<Teacher<String, String>> aiTeachers = manager.findTeachersBySubject("人工智能");
        for (Teacher<String, String> teacher : aiTeachers) {
            System.out.println("- " + teacher.getName() + " (教师编号: " + teacher.getTeacherId() + ")");
        }
        System.out.println();
        
        // 显示统计信息
        System.out.println("5. 统计信息：");
        manager.showStatistics();
        System.out.println();
        
        // 演示老师的教学方法
        System.out.println("6. 演示老师教学：");
        teacher1.teach();
        teacher1.teach("高等数学");
        teacher1.showAllSubjects();
        System.out.println();
        
        // 演示泛型的类型安全和多态
        System.out.println("7. 演示泛型类型安全：");
        demonstrateGenericTypeSafety();
        System.out.println();
        
        // 演示不同泛型类型的使用
        System.out.println("8. 演示不同泛型类型组合：");
        demonstrateDifferentGenericTypes();
        
        System.out.println("\n=== 演示程序结束 ===");
    }
    
    /**
     * 演示泛型的类型安全特性
     */
    private static void demonstrateGenericTypeSafety() {
        // String类型的Person
        Person<String> stringPerson = new Person<>("字符串姓名", 25);
        String name1 = stringPerson.getName(); // 编译时确保返回String类型
        
        // StringBuilder类型的Person
        Person<StringBuilder> sbPerson = new Person<>(new StringBuilder("StringBuilder姓名"), 30);
        StringBuilder name2 = sbPerson.getName(); // 编译时确保返回StringBuilder类型
        
        System.out.println("String类型姓名: " + name1 + " (类型: " + name1.getClass().getSimpleName() + ")");
        System.out.println("StringBuilder类型姓名: " + name2 + " (类型: " + name2.getClass().getSimpleName() + ")");
        
        // 这展示了泛型如何在编译时提供类型安全
        // 如果尝试将错误类型赋值，编译器会报错
    }
    
    /**
     * 演示不同泛型类型组合的使用
     */
    private static void demonstrateDifferentGenericTypes() {
        // 创建一个使用StringBuilder作为姓名类型的老师管理器
        TeacherManager<StringBuilder, String> sbManager = new TeacherManager<>();
        
        // 创建使用StringBuilder姓名的老师
        StringBuilder teacherName = new StringBuilder("StringBuilder老师");
        Teacher<StringBuilder, String> sbTeacher = new Teacher<>(
            teacherName, 38, "女", "物理", "T004", 12
        );
        
        sbManager.addTeacher(sbTeacher);
        
        System.out.println("使用StringBuilder类型姓名的老师：");
        sbManager.displayAllTeachers();
        
        // 演示StringBuilder的可变性
        teacherName.append(" (修改后)");
        System.out.println("修改StringBuilder后的姓名: " + sbTeacher.getName());
    }
}
