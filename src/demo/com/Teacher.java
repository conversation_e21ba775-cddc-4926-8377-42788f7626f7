package demo.com;

import java.util.ArrayList;
import java.util.List;

/**
 * 老师类，继承自Person类，展示泛型的使用
 * @param <T> 姓名的类型
 * @param <S> 学科类型，可以是String或其他类型
 */
public class Teacher<T, S> extends Person<T> {
    
    private S subject;           // 教授的学科
    private String teacherId;    // 教师编号
    private List<S> subjects;    // 教授的多个学科
    private int yearsOfExperience; // 教学经验年数
    
    // 默认构造函数
    public Teacher() {
        super();
        this.subjects = new ArrayList<>();
    }
    
    // 基础构造函数
    public Teacher(T name, int age, S subject) {
        super(name, age);
        this.subject = subject;
        this.subjects = new ArrayList<>();
        this.subjects.add(subject);
    }
    
    // 完整构造函数
    public Teacher(T name, int age, String gender, S subject, String teacherId, int yearsOfExperience) {
        super(name, age, gender);
        this.subject = subject;
        this.teacherId = teacherId;
        this.yearsOfExperience = yearsOfExperience;
        this.subjects = new ArrayList<>();
        this.subjects.add(subject);
    }
    
    // Getter和Setter方法
    public S getSubject() {
        return subject;
    }
    
    public void setSubject(S subject) {
        this.subject = subject;
    }
    
    public String getTeacherId() {
        return teacherId;
    }
    
    public void setTeacherId(String teacherId) {
        this.teacherId = teacherId;
    }
    
    public List<S> getSubjects() {
        return subjects;
    }
    
    public void setSubjects(List<S> subjects) {
        this.subjects = subjects;
    }
    
    public int getYearsOfExperience() {
        return yearsOfExperience;
    }
    
    public void setYearsOfExperience(int yearsOfExperience) {
        this.yearsOfExperience = yearsOfExperience;
    }
    
    // 添加学科
    public void addSubject(S newSubject) {
        if (!subjects.contains(newSubject)) {
            subjects.add(newSubject);
        }
    }
    
    // 移除学科
    public void removeSubject(S subjectToRemove) {
        subjects.remove(subjectToRemove);
    }
    
    // 老师特有的方法
    public void teach() {
        System.out.println("老师 " + getName() + " 正在教授 " + subject);
    }
    
    public void teach(S specificSubject) {
        System.out.println("老师 " + getName() + " 正在教授 " + specificSubject);
    }
    
    public void showAllSubjects() {
        System.out.println("老师 " + getName() + " 教授的所有学科：");
        for (S sub : subjects) {
            System.out.println("- " + sub);
        }
    }
    
    // 重写introduce方法
    @Override
    public void introduce() {
        super.introduce();
        System.out.println("我是一名老师，教师编号：" + teacherId);
        System.out.println("主要教授学科：" + subject);
        System.out.println("教学经验：" + yearsOfExperience + " 年");
    }
    
    @Override
    public String toString() {
        return "Teacher{" +
                "name=" + getName() +
                ", age=" + getAge() +
                ", gender=" + getGender() +
                ", subject=" + subject +
                ", teacherId='" + teacherId + '\'' +
                ", subjects=" + subjects +
                ", yearsOfExperience=" + yearsOfExperience +
                '}';
    }
}
